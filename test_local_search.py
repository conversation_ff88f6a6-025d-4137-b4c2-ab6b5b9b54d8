#!/usr/bin/env python3
"""
测试local_search函数的简单脚本
"""

import networkx as nx
from base_fun import gen_graph, local_search, PRE

def test_local_search():
    """测试local_search函数"""
    
    # 创建一个简单的测试图
    G = nx.Graph()
    G.add_edges_from([(1, 2), (2, 3), (3, 4), (4, 5), (1, 5), (2, 6), (3, 7)])
    
    # 参数设置
    p = 0.1
    k = 3
    max_hop = 3
    
    # 生成邻居字典
    neighbors = {v: list(G.neighbors(v)) for v in G.nodes()}
    
    # 初始种子集合
    initial_seed = [1, 2, 3]
    
    print("=== 局部搜索测试 ===")
    print(f"初始种子集合: {initial_seed}")
    
    # 计算初始适应度
    initial_fitness = PRE(G, set(initial_seed), p, neighbors, max_hop)
    print(f"初始PRE适应度: {initial_fitness:.4f}")
    
    # 执行局部搜索
    optimized_seed = local_search(initial_seed, G, p, k, neighbors, max_hop)
    print(f"优化后种子集合: {optimized_seed}")
    
    # 计算优化后适应度
    optimized_fitness = PRE(G, set(optimized_seed), p, neighbors, max_hop)
    print(f"优化后PRE适应度: {optimized_fitness:.4f}")
    
    # 计算提升
    improvement = optimized_fitness - initial_fitness
    print(f"适应度提升: {improvement:.4f}")
    
    if improvement > 0:
        print("✓ 局部搜索成功找到更好的解")
    else:
        print("- 局部搜索未找到更好的解")

if __name__ == "__main__":
    test_local_search()
